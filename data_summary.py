#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
- 开发作者: 学文
- 开发时间: 2025-01-15
- 模块说明: Excel数据汇总脚本，按单据日期和规格型号分组汇总数值字段
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime


def load_and_validate_data(file_path='1.xlsx'):
    """
    加载和验证Excel数据文件
    
    Args:
        file_path (str): Excel文件路径
        
    Returns:
        pd.DataFrame: 加载的数据框
        
    Raises:
        FileNotFoundError: 文件不存在
        Exception: 其他读取错误
    """
    print(f"正在读取文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在，请检查文件路径")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"✓ 成功读取文件，共 {len(df)} 行数据，{len(df.columns)} 个字段")
        
        # 验证必要字段是否存在
        required_fields = ['单据日期', '规格型号', '数量', '销售含税单价', '含税总金额']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")
        
        print("✓ 数据字段验证通过")
        return df
        
    except Exception as e:
        raise Exception(f"读取Excel文件时出错: {str(e)}")


def preprocess_data(df):
    """
    数据预处理：处理空值、数据类型转换等
    
    Args:
        df (pd.DataFrame): 原始数据框
        
    Returns:
        pd.DataFrame: 预处理后的数据框
    """
    print("正在进行数据预处理...")
    
    # 创建数据副本避免修改原数据
    processed_df = df.copy()
    
    # 处理规格型号空值，替换为默认值
    null_spec_count = processed_df['规格型号'].isnull().sum()
    if null_spec_count > 0:
        processed_df['规格型号'].fillna('AAABBBCCC', inplace=True)
        print(f"✓ 已处理 {null_spec_count} 个规格型号空值，替换为 'AAABBBCCC'")
    
    # 确保单据日期为字符串格式（便于分组）
    processed_df['单据日期'] = processed_df['单据日期'].astype(str)
    
    # 确保数值字段为数值类型
    numeric_fields = ['数量', '销售含税单价', '含税总金额']
    for field in numeric_fields:
        processed_df[field] = pd.to_numeric(processed_df[field], errors='coerce')
        
        # 检查转换后的空值
        null_count = processed_df[field].isnull().sum()
        if null_count > 0:
            print(f"⚠ 警告: {field} 字段有 {null_count} 个无效数值，将用0填充")
            processed_df[field].fillna(0, inplace=True)
    
    print("✓ 数据预处理完成")
    return processed_df


def perform_summary(df):
    """
    执行数据汇总：按单据日期和规格型号分组汇总
    
    Args:
        df (pd.DataFrame): 预处理后的数据框
        
    Returns:
        pd.DataFrame: 汇总后的数据框
    """
    print("正在执行数据汇总...")
    
    # 按单据日期和规格型号分组，对数值字段求和
    summary_df = df.groupby(['单据日期', '规格型号']).agg({
        '数量': 'sum',
        '销售含税单价': 'sum', 
        '含税总金额': 'sum'
    }).reset_index()
    
    # 重命名汇总字段以明确含义
    summary_df.rename(columns={
        '数量': '汇总数量',
        '销售含税单价': '汇总销售含税单价',
        '含税总金额': '汇总含税总金额'
    }, inplace=True)
    
    print(f"✓ 汇总完成，原始 {len(df)} 行数据汇总为 {len(summary_df)} 行")
    return summary_df


def export_results(summary_df, output_file='summary_1.xlsx'):
    """
    导出汇总结果到Excel文件
    
    Args:
        summary_df (pd.DataFrame): 汇总数据框
        output_file (str): 输出文件名
    """
    print(f"正在导出结果到: {output_file}")
    
    try:
        # 导出到Excel文件
        summary_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"✓ 成功导出汇总结果到 {output_file}")
        
        # 显示文件大小信息
        file_size = os.path.getsize(output_file)
        print(f"✓ 文件大小: {file_size} 字节")
        
    except Exception as e:
        raise Exception(f"导出文件时出错: {str(e)}")


def display_preview(summary_df, original_df):
    """
    在控制台显示汇总结果预览和统计信息
    
    Args:
        summary_df (pd.DataFrame): 汇总数据框
        original_df (pd.DataFrame): 原始数据框
    """
    print("\n" + "="*60)
    print("汇总结果预览")
    print("="*60)
    
    # 显示前10行汇总结果
    print("\n前10行汇总数据:")
    print("-" * 60)
    print(summary_df.head(10).to_string(index=False))
    
    print("\n" + "="*60)
    print("汇总统计信息")
    print("="*60)
    
    # 统计信息
    print(f"原始数据行数: {len(original_df)}")
    print(f"汇总后行数: {len(summary_df)}")
    print(f"数据压缩率: {(1 - len(summary_df)/len(original_df))*100:.1f}%")
    
    # 数值字段统计
    print(f"\n汇总数值统计:")
    print(f"总数量: {summary_df['汇总数量'].sum():,.0f}")
    print(f"总销售含税单价: {summary_df['汇总销售含税单价'].sum():,.2f}")
    print(f"总含税金额: {summary_df['汇总含税总金额'].sum():,.2f}")
    
    # 分组统计
    print(f"\n分组统计:")
    print(f"不同单据日期数: {summary_df['单据日期'].nunique()}")
    print(f"不同规格型号数: {summary_df['规格型号'].nunique()}")


def main():
    """
    主函数：整合所有功能模块
    """
    print("="*60)
    print("Excel数据汇总脚本")
    print("开发作者: 学文")
    print("开发时间: 2025-01-15")
    print("="*60)
    
    try:
        # 1. 加载和验证数据
        original_df = load_and_validate_data('1.xlsx')
        
        # 2. 数据预处理
        processed_df = preprocess_data(original_df)
        
        # 3. 执行汇总
        summary_df = perform_summary(processed_df)
        
        # 4. 导出结果
        export_results(summary_df, 'summary_1.xlsx')
        
        # 5. 显示预览
        display_preview(summary_df, original_df)
        
        print("\n" + "="*60)
        print("✓ 数据汇总任务完成！")
        print("✓ 汇总结果已保存到 summary_1.xlsx")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {str(e)}")
        print("请检查输入文件和数据格式")
        sys.exit(1)


if __name__ == "__main__":
    main()
